package com.suncent.smc.persistence.promotion.domain.entity;

import com.suncent.smc.common.annotation.Excel;
import com.suncent.smc.common.core.domain.BaseEntity;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 促销RPA任务对象 am_promotion_rpa_task
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AmPromotionRpaTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 关联BD记录ID
     */
    @Excel(name = "关联BD记录ID")
    private Long refBestDealId;

    /**
     * 操作类型，ADD 创建,UPDATE 更新,DELETE取消
     */
    @Excel(name = "操作类型", readConverterExp = "ADD=创建,UPDATE=更新,DELETE=取消")
    private String operationType;

    /**
     * 促销ID，亚马逊后台的ID
     */
    @Excel(name = "促销ID")
    private String promotionId;

    /**
     * 活动状态
     */
    @Excel(name = "活动状态")
    private String promotionStatus;

    /**
     * 刊登类型，5：VCDF，6：VCPO
     */
    @Excel(name = "刊登类型", readConverterExp = "5=VCDF,6=VCPO")
    private Integer publishType;

    /**
     * 站点
     */
    @Excel(name = "站点")
    private String site;

    /**
     * 活动类型,1:BD,2:Coupon,3:PD
     */
    @Excel(name = "活动类型", readConverterExp = "1=BD,2=Coupon,3=PD")
    private Integer activityType;

    /**
     * 执行任务JSON
     */
    private String executeJson;

    /**
     * 状态：1：待处理，2：处理中，3：处理成功，4：处理失败
     */
    @Excel(name = "状态", readConverterExp = "1=待处理,2=处理中,3=处理成功,4=处理失败")
    private Integer status;

    /**
     * SMC处理标识
     */
    @Excel(name = "SMC处理标识")
    private Integer handleFlag;

    /**
     * 错误消息
     */
    @Excel(name = "错误消息")
    private String errorMsg;

    /**
     * 备注
     */
    @Excel(name = "备注")
    private String remark;

    /**
     * 删除标记,1是，0否
     */
    private Integer delFlag;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        ADD("ADD", "创建"),
        UPDATE("UPDATE", "更新"),
        DELETE("DELETE", "取消");

        private final String code;
        private final String desc;

        OperationType(String code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public String getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 刊登类型枚举
     */
    public enum PublishType {
        VCDF(5, "VCDF"),
        VCPO(6, "VCPO");

        private final int code;
        private final String desc;

        PublishType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 活动类型枚举
     */
    public enum ActivityType {
        BD(1, "BD"),
        COUPON(2, "Coupon"),
        PD(3, "PD");

        private final int code;
        private final String desc;

        ActivityType(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }

    /**
     * 状态枚举
     */
    public enum Status {
        PENDING(1, "待处理"),
        PROCESSING(2, "处理中"),
        SUCCESS(3, "处理成功"),
        FAILED(4, "处理失败");

        private final int code;
        private final String desc;

        Status(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public int getCode() {
            return code;
        }

        public String getDesc() {
            return desc;
        }
    }
}

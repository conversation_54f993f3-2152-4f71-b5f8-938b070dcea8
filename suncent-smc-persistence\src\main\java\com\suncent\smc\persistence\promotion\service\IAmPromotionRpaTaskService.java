package com.suncent.smc.persistence.promotion.service;

import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask;

import java.util.List;

/**
 * 促销RPA任务Service接口
 *
 * <AUTHOR>
 * @date 2025-07-31
 */
public interface IAmPromotionRpaTaskService {
    /**
     * 查询促销RPA任务
     *
     * @param id 促销RPA任务主键
     * @return 促销RPA任务
     */
    public AmPromotionRpaTask selectAmPromotionRpaTaskById(Long id);

    /**
     * 查询促销RPA任务列表
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskList(AmPromotionRpaTask amPromotionRpaTask);

    /**
     * 新增促销RPA任务
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 结果
     */
    public int insertAmPromotionRpaTask(AmPromotionRpaTask amPromotionRpaTask);

    /**
     * 修改促销RPA任务
     *
     * @param amPromotionRpaTask 促销RPA任务
     * @return 结果
     */
    public int updateAmPromotionRpaTask(AmPromotionRpaTask amPromotionRpaTask);

    /**
     * 批量删除促销RPA任务
     *
     * @param ids 需要删除的促销RPA任务主键集合
     * @return 结果
     */
    public int deleteAmPromotionRpaTaskByIds(String ids);

    /**
     * 删除促销RPA任务信息
     *
     * @param id 促销RPA任务主键
     * @return 结果
     */
    public int deleteAmPromotionRpaTaskById(Long id);

    /**
     * 根据BD记录ID查询RPA任务列表
     *
     * @param refBestDealId BD记录ID
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByRefId(Long refBestDealId);

    /**
     * 根据促销ID查询RPA任务列表
     *
     * @param promotionId 促销ID
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByPromotionId(String promotionId);

    /**
     * 查询待处理的RPA任务
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectPendingAmPromotionRpaTasks();

    /**
     * 查询处理中的RPA任务
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectProcessingAmPromotionRpaTasks();

    /**
     * 查询已完成的RPA任务（成功或失败）
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectCompletedAmPromotionRpaTasks();

    /**
     * 根据状态查询RPA任务
     *
     * @param status 状态
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByStatus(Integer status);

    /**
     * 根据活动类型查询RPA任务
     *
     * @param activityType 活动类型
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByActivityType(Integer activityType);

    /**
     * 根据操作类型查询RPA任务
     *
     * @param operationType 操作类型
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectAmPromotionRpaTaskByOperationType(String operationType);

    /**
     * 查询需要SMC处理的任务
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectTasksNeedSmcHandle();

    /**
     * 批量更新SMC处理标识
     *
     * @param ids        任务ID集合
     * @param handleFlag 处理标识
     * @return 结果
     */
    public int batchUpdateHandleFlag(List<Long> ids, Integer handleFlag);

    /**
     * 查询需要同步状态的RPA任务（del_flag=0 且 handle_flag=0）
     *
     * @return 促销RPA任务集合
     */
    public List<AmPromotionRpaTask> selectTasksForStatusSync();

    /**
     * 创建促销RPA任务
     *
     * @param refBestDealId BD记录ID
     * @param operationType 操作类型
     * @param promotionId   促销ID
     * @param publishType   刊登类型
     * @param site          站点
     * @param activityType  活动类型
     * @param executeJson   执行JSON
     * @param createBy      创建人
     * @return 结果
     */
    public int createPromotionRpaTask(Long refBestDealId, String operationType, String promotionId,
                                      Integer publishType, String site, Integer activityType,
                                      String executeJson, String createBy);

    /**
     * 创建促销RPA任务（支持promotion_status）
     *
     * @param refBestDealId   BD记录ID
     * @param operationType   操作类型
     * @param promotionId     促销ID
     * @param promotionStatus 促销状态
     * @param publishType     刊登类型
     * @param site            站点
     * @param activityType    活动类型
     * @param executeJson     执行JSON
     * @param createBy        创建人
     * @return 结果
     */
    public int createPromotionRpaTaskWithStatus(Long refBestDealId, String operationType, String promotionId,
                                               String promotionStatus, Integer publishType, String site,
                                               Integer activityType, String executeJson, String createBy);

    /**
     * 更新任务状态
     *
     * @param id       任务ID
     * @param status   状态
     * @param errorMsg 错误消息（可选）
     * @param updateBy 更新人
     * @return 结果
     */
    public int updateTaskStatus(Long id, Integer status, String errorMsg, String updateBy);

    /**
     * 标记任务为SMC已处理
     *
     * @param id       任务ID
     * @param updateBy 更新人
     * @return 结果
     */
    public int markTaskAsHandled(Long id, String updateBy);
}

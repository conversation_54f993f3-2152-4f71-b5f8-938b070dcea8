<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.promotion.mapper.AmPromotionRpaTaskMapper">

    <resultMap type="com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask"
               id="AmPromotionRpaTaskResult">
        <result property="id" column="id"/>
        <result property="refBestDealId" column="ref_best_deal_id"/>
        <result property="operationType" column="operation_type"/>
        <result property="promotionId" column="promotion_id"/>
        <result property="promotionStatus" column="promotion_status"/>
        <result property="publishType" column="publish_type"/>
        <result property="site" column="site"/>
        <result property="activityType" column="activity_type"/>
        <result property="executeJson" column="execute_json"/>
        <result property="status" column="status"/>
        <result property="handleFlag" column="handle_flag"/>
        <result property="errorMsg" column="error_msg"/>
        <result property="remark" column="remark"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectAmPromotionRpaTaskVo">
        select id,
               ref_best_deal_id,
               operation_type,
               promotion_id,
               promotion_status,
               publish_type,
               site,
               activity_type,
               execute_json,
               status,
               create_by,
               create_time,
               update_by,
               update_time,
               handle_flag,
               error_msg,
               remark,
               del_flag
        from am_promotion_rpa_task
    </sql>

    <select id="selectAmPromotionRpaTaskList"
            parameterType="com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask"
            resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        <where>
            <if test="refBestDealId != null ">and ref_best_deal_id = #{refBestDealId}</if>
            <if test="operationType != null  and operationType != ''">and operation_type = #{operationType}</if>
            <if test="promotionId != null  and promotionId != ''">and promotion_id = #{promotionId}</if>
            <if test="promotionStatus != null  and promotionStatus != ''">and promotion_status = #{promotionStatus}</if>
            <if test="publishType != null ">and publish_type = #{publishType}</if>
            <if test="site != null  and site != ''">and site = #{site}</if>
            <if test="activityType != null ">and activity_type = #{activityType}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="handleFlag != null ">and handle_flag = #{handleFlag}</if>
            and del_flag = 0
        </where>
        order by create_time desc
    </select>

    <select id="selectAmPromotionRpaTaskById" parameterType="Long" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <select id="selectAmPromotionRpaTaskByRefId" parameterType="Long" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where ref_best_deal_id = #{refBestDealId} and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectAmPromotionRpaTaskByPromotionId" parameterType="String" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where promotion_id = #{promotionId} and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectPendingAmPromotionRpaTasks" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where status = 1 and del_flag = 0
        order by create_time asc
    </select>

    <select id="selectProcessingAmPromotionRpaTasks" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where status = 2 and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectCompletedAmPromotionRpaTasks" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where status in (3, 4) and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectAmPromotionRpaTaskByStatus" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where status = #{status} and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectAmPromotionRpaTaskByActivityType" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where activity_type = #{activityType} and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectAmPromotionRpaTaskByOperationType" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where operation_type = #{operationType} and del_flag = 0
        order by create_time desc
    </select>

    <select id="selectTasksNeedSmcHandle" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where status in (3, 4) and handle_flag = 0 and del_flag = 0
        order by create_time desc
    </select>

    <insert id="insertAmPromotionRpaTask"
            parameterType="com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask"
            useGeneratedKeys="true" keyProperty="id">
        insert into am_promotion_rpa_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="refBestDealId != null">ref_best_deal_id,</if>
            <if test="operationType != null and operationType != ''">operation_type,</if>
            <if test="promotionId != null and promotionId != ''">promotion_id,</if>
            <if test="promotionStatus != null and promotionStatus != ''">promotion_status,</if>
            <if test="publishType != null">publish_type,</if>
            <if test="site != null and site != ''">site,</if>
            <if test="activityType != null">activity_type,</if>
            <if test="executeJson != null">execute_json,</if>
            <if test="status != null">status,</if>
            <if test="handleFlag != null">handle_flag,</if>
            <if test="errorMsg != null">error_msg,</if>
            <if test="remark != null">remark,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="refBestDealId != null">#{refBestDealId},</if>
            <if test="operationType != null and operationType != ''">#{operationType},</if>
            <if test="promotionId != null and promotionId != ''">#{promotionId},</if>
            <if test="promotionStatus != null and promotionStatus != ''">#{promotionStatus},</if>
            <if test="publishType != null">#{publishType},</if>
            <if test="site != null and site != ''">#{site},</if>
            <if test="activityType != null">#{activityType},</if>
            <if test="executeJson != null">#{executeJson},</if>
            <if test="status != null">#{status},</if>
            <if test="handleFlag != null">#{handleFlag},</if>
            <if test="errorMsg != null">#{errorMsg},</if>
            <if test="remark != null">#{remark},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="updateAmPromotionRpaTask"
            parameterType="com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask">
        update am_promotion_rpa_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="refBestDealId != null">ref_best_deal_id = #{refBestDealId},</if>
            <if test="operationType != null and operationType != ''">operation_type = #{operationType},</if>
            <if test="promotionId != null and promotionId != ''">promotion_id = #{promotionId},</if>
            <if test="promotionStatus != null and promotionStatus != ''">promotion_status = #{promotionStatus},</if>
            <if test="publishType != null">publish_type = #{publishType},</if>
            <if test="site != null and site != ''">site = #{site},</if>
            <if test="activityType != null">activity_type = #{activityType},</if>
            <if test="executeJson != null">execute_json = #{executeJson},</if>
            <if test="status != null">status = #{status},</if>
            <if test="handleFlag != null">handle_flag = #{handleFlag},</if>
            <if test="errorMsg != null">error_msg = #{errorMsg},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAmPromotionRpaTaskById" parameterType="Long">
        update am_promotion_rpa_task
        set del_flag = 1
        where id = #{id}
    </delete>

    <delete id="deleteAmPromotionRpaTaskByIds" parameterType="String">
        update am_promotion_rpa_task set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <update id="batchUpdateHandleFlag">
        update am_promotion_rpa_task set handle_flag = #{handleFlag}
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectTasksForStatusSync" resultMap="AmPromotionRpaTaskResult">
        <include refid="selectAmPromotionRpaTaskVo"/>
        where del_flag = 0 and handle_flag = 0
        order by create_time asc
    </select>
</mapper>

<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('BD促销记录列表')" />
    <th:block th:include="include :: datetimepicker-css" />
</head>
<body class="gray-bg">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>促销名称：</label>
                                <input type="text" name="promotionName" placeholder="请输入促销名称"/>
                            </li>
                            <li>
                                <label>状态：</label>
                                <select name="status">
                                    <option value="">所有</option>
                                    <option value="DRAFT">DRAFT</option>
                                    <option value="NEEDS_YOUR_ATTENTION">NEEDS_YOUR_ATTENTION</option>
                                    <option value="APPROVED">APPROVED</option>
                                    <option value="CANCELED">CANCELED</option>
                                </select>
                            </li>
                            <li>
                                <label>刊登类型：</label>
                                <select name="publishType">
                                    <option value="">所有</option>
                                    <option value="5">VCDF</option>
                                    <option value="6">VCPO</option>
                                </select>
                            </li>
                            <li>
                                <label>关键字：</label>
                                <input type="text" name="keyword" placeholder="促销名称或促销ID"/>
                            </li>
                            <li class="select-time">
                                <label>创建时间：</label>
                                <input type="text" class="time-input" id="startTime" placeholder="开始时间" name="params[beginTime]" readonly/>
                                <span>-</span>
                                <input type="text" class="time-input" id="endTime" placeholder="结束时间" name="params[endTime]" readonly/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="promotion:record:add">
                    <i class="fa fa-plus"></i> 批量新建
                </a>
                <a class="btn btn-success" onclick="$.operate.confirm()" shiro:hasPermission="promotion:record:confirm">
                    <i class="fa fa-check"></i> 批量确认
                </a>
                <a class="btn btn-warning" onclick="$.operate.import()" shiro:hasPermission="promotion:record:import">
                    <i class="fa fa-upload"></i> 批量导入
                </a>
                <a class="btn btn-warning" onclick="$.operate.cancel()" shiro:hasPermission="promotion:record:cancel">
                    <i class="fa fa-times"></i> 批量取消
                </a> 
                <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="promotion:record:export">
                    <i class="fa fa-download"></i> 导出
                </a>
            
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var editFlag = [[${@permission.hasPermi('promotion:record:edit')}]];
        var removeFlag =[[${@permission.hasPermi('promotion:record:remove')}]];
        var logFlag =[[${@permission.hasPermi('promotion:record:view')}]];
        var prefix = ctx + "promotion/record";

        $(function() {
            console.log('Document ready, initializing table...');
            console.log('jQuery version:', $.fn.jquery);
            console.log('Bootstrap table available:', typeof $.fn.bootstrapTable);
            console.log('Table element:', $('#bootstrap-table'));

            // 初始化日期选择器
            $("#startTime").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                language: 'zh-CN',
                autoclose: true,
                todayBtn: true
            });

            $("#endTime").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                language: 'zh-CN',
                autoclose: true,
                todayBtn: true
            });

            var options = {
                url: prefix + "/list",  // 暂时注释掉API调用
                // data: testData,  // 使用静态测试数据
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "BD促销记录",
                sortName: "createTime",
                sortOrder: "desc",
                pageSize: 25,
                columns: [{
                    checkbox: true
                }, {
                    field: 'id',
                    title: 'ID',
                    visible: false
                }, {
                    field: 'promotionName',
                    title: '促销名称',
                }, {
                    field: 'promotionId',
                    title: '促销ID',
                },  {
                    field: 'publishType',
                    title: '刊登类型',
                    formatter: function(value, row, index) {
                        if (value == 5) return '<span class="label label-primary">VCDF</span>';
                        if (value == 6) return '<span class="label label-info">VCPO</span>';
                        return '<span class="label label-default">未知</span>';
                    }
                }, {
                    field: 'dealType',
                    title: '促销类型',
                    formatter: function(value, row, index) {
                        if (value == 1) return '<span class="label label-primary">Best Deal</span>';
                        if (value == 2) return '<span class="label label-info">Lightning Deal</span>';
                        return '<span class="label label-default">未知</span>';
                    }
                },
                 {
                    field: 'eventType',
                    title: '事件类型',
                    formatter: function(value, row, index) {
                        if (value == 1) return '<span class="label label-primary">自定义日期</span>';
                        if (value == 2) return '<span class="label label-info">会员日</span>';
                        if (value == 3) return '<span class="label label-success">黑五</span>';
                        return '<span class="label label-default">未知</span>';
                    }
                }, {
                    field: 'status',
                    title: '状态',
                    formatter: function(value, row, index) {
                        return '<span class="label label-default">' + value + '</span>';
                    }
                }, {
                    field: 'asinCount',
                    title: 'ASIN数量',
                    formatter: function(value, row, index) {
                        return '<span class="badge badge-primary">' + (value || 0) + '</span>';
                    }
                }, {
                    field: 'startDateUtc',
                    title: '开始时间',
                }, {
                    field: 'endDateUtc',
                    title: '结束时间',
                }, {
                    field: 'createTime',
                    title: '创建时间',
                }, {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        // 编辑按钮 - 使用弹窗模式
                        // if (editFlag) {
                            actions.push('<a class="btn btn-success btn-xs" href="javascript:void(0)" onclick="editRecord(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        // }
                        // 日志按钮 - 需要查看权限
                        // if (logFlag) {
                            actions.push('<a class="btn btn-info btn-xs" href="javascript:void(0)" onclick="showLogs(\'' + row.id + '\')"><i class="fa fa-list"></i>日志</a> ');
                        // }
                        // 删除按钮 - 需要删除权限
                        // if (removeFlag) {
                        //     actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.id + '\')"><i class="fa fa-remove"></i>删除</a>');
                        // }
                        return actions.join('');
                    }
                }]
            };

            console.log('Initializing table with options:', options);
            console.log('Table element exists:', $('#bootstrap-table').length > 0);
            console.log('Prefix:', prefix);

            $.table.init(options);
        });

        // 显示操作日志
        function showLogs(id) {
            var options = {
                title: 'BD促销综合日志',
                url: prefix + "/logsIntegrated/" + id,
                width: 1200,
                height: 700,
                btn: ['<i class="fa fa-close"></i> 关闭'],
            };
            $.modal.openOptions(options);
        }

        // 自定义新增方法 - 打开批量工作区
        $.operate.add = function() {
            $.modal.openTab("新增BD促销", prefix + "/add" );
        }

        // 编辑记录 - 使用弹窗模式
        function editRecord(recordId) {
            // 直接打开编辑弹窗，通过recordId参数传递
            var options = {
                title: '编辑BD促销',
                url: prefix + "/editModal?id=" + recordId,
                width: '1200',
                height: '700',
                callBack: function(index, layero) {
                    try {
                        // 获取子页面的window对象
                        var iframe = layero.find('iframe')[0];
                        var childWindow = iframe.contentWindow;

                        // 调用子页面的submitHandler函数进行验证和数据准备
                        if (childWindow && typeof childWindow.submitHandler === 'function') {
                            var submitResult = childWindow.submitHandler(index, layero);

                            if (submitResult && childWindow.dealData) {
                                // 验证通过且有数据，提交到后端
                                var updatedData = childWindow.dealData;
                                updatedData.id = recordId; // 确保ID正确

                                // 调用后端更新接口
                                $.ajax({
                                    url: prefix + "/edit",
                                    type: "POST",
                                    contentType: "application/json",
                                    dataType: "json",
                                    data: JSON.stringify(updatedData),
                                    success: function(result) {
                                        if (result.code === 0) {
                                            $.modal.msgSuccess("修改成功");
                                            $.modal.close(index);
                                            // 刷新表格
                                            $("#bootstrap-table").bootstrapTable('refresh');
                                        } else {
                                            $.modal.alertError("修改失败：" + (result.msg || "未知错误"));
                                        }
                                    },
                                    error: function(xhr, status, error) {
                                        $.modal.alertError('修改失败：' + error);
                                    }
                                });
                            } else {
                                // 验证失败，不关闭窗口
                                return false;
                            }
                        } else {
                            console.error('无法找到子页面的submitHandler函数');
                            return false;
                        }
                    } catch (e) {
                        console.error('调用子页面函数失败:', e);
                        return false;
                    }
                }
            };
            $.modal.openOptions(options);
        }

        // 获取状态显示名称
        function getStatusDisplayName(status) {
            switch(status) {
                case 'DRAFT': return '草稿';
                case 'NEEDS_YOUR_ATTENTION': return '需要关注';
                case 'APPROVED': return '已批准';
                case 'CANCELED': return '已取消';
                default: return status;
            }
        }
    </script>
</body>
</html>

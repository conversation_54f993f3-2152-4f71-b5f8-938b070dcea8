package com.suncent.smc.quartz.task.promotion;

import com.suncent.smc.common.constant.UserConstants;
import com.suncent.smc.common.utils.DateUtils;
import com.suncent.smc.persistence.promotion.domain.entity.AmPromotionRpaTask;
import com.suncent.smc.persistence.promotion.service.IAmBestDealRecordService;
import com.suncent.smc.persistence.promotion.service.IAmPromotionRpaTaskService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 促销RPA任务状态同步定时任务
 * 
 * 功能：处理 am_promotion_rpa_task 表中 del_flag=0 且 handle_flag=0 的数据，
 * 根据数据的 status 更新 am_best_deal_record 中对应 promotion_id 的 status
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@Component
@Slf4j
public class PromotionRpaStatusSyncTask {

    @Autowired
    private IAmPromotionRpaTaskService amPromotionRpaTaskService;

    @Autowired
    private IAmBestDealRecordService amBestDealRecordService;

    /**
     * 促销RPA任务状态同步定时任务
     */
    @XxlJob("PromotionRpaStatusSyncTask")
    public ReturnT<String> execute() {
        log.info("开始执行促销RPA任务状态同步定时任务，当前时间：{}", DateUtils.getTime());
        
        try {
            // 1. 查询需要同步状态的RPA任务（del_flag=0 且 handle_flag=0）
            List<AmPromotionRpaTask> pendingTasks = amPromotionRpaTaskService.selectTasksForStatusSync();
            
            if (CollectionUtils.isEmpty(pendingTasks)) {
                log.info("没有需要同步状态的RPA任务");
                return ReturnT.SUCCESS;
            }
            
            log.info("查询到 {} 条需要同步状态的RPA任务", pendingTasks.size());
            
            // 2. 处理每个任务
            int successCount = 0;
            int failCount = 0;
            List<Long> processedTaskIds = new ArrayList<>();
            
            for (AmPromotionRpaTask task : pendingTasks) {
                try {
                    boolean success = processTask(task);
                    if (success) {
                        successCount++;
                        processedTaskIds.add(task.getId());
                    } else {
                        failCount++;
                    }
                } catch (Exception e) {
                    log.error("处理RPA任务失败，任务ID：{}，促销ID：{}，错误：{}", 
                            task.getId(), task.getPromotionId(), e.getMessage(), e);
                    failCount++;
                }
            }
            
            // 3. 批量更新已处理任务的handle_flag
            if (!processedTaskIds.isEmpty()) {
                try {
                    amPromotionRpaTaskService.batchUpdateHandleFlag(processedTaskIds, 1);
                    log.info("批量更新handle_flag成功，任务数量：{}", processedTaskIds.size());
                } catch (Exception e) {
                    log.error("批量更新handle_flag失败：{}", e.getMessage(), e);
                }
            }
            
            log.info("促销RPA任务状态同步完成，成功：{}，失败：{}", successCount, failCount);
            
            return ReturnT.SUCCESS;
            
        } catch (Exception e) {
            log.error("促销RPA任务状态同步定时任务执行异常", e);
            return new ReturnT<>(ReturnT.FAIL_CODE, "任务执行失败：" + e.getMessage());
        }
    }

    /**
     * 处理单个RPA任务
     * 
     * @param task RPA任务
     * @return 是否处理成功
     */
    private boolean processTask(AmPromotionRpaTask task) {
        try {
            // 根据RPA任务状态映射到BD记录状态
            String bdStatus = mapRpaStatusToBdStatus(task.getStatus());
            
            if (bdStatus == null) {
                log.warn("RPA任务状态无法映射到BD状态，任务ID：{}，状态：{}", task.getId(), task.getStatus());
                return false;
            }
            
            // 更新BD记录状态
            int updateResult = amBestDealRecordService.updateStatusByPromotionId(
                    task.getPromotionId(), bdStatus, UserConstants.SYS_USER_ID);
            
            if (updateResult > 0) {
                log.info("更新BD记录状态成功，促销ID：{}，新状态：{}", task.getPromotionId(), bdStatus);
                return true;
            } else {
                log.warn("更新BD记录状态失败，可能不存在对应的促销ID：{}", task.getPromotionId());
                return false;
            }
            
        } catch (Exception e) {
            log.error("处理RPA任务异常，任务ID：{}，促销ID：{}，错误：{}", 
                    task.getId(), task.getPromotionId(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 将RPA任务状态映射到BD记录状态
     * 
     * RPA任务状态：1：待处理，2：处理中，3：处理成功，4：处理失败
     * BD记录状态：DRAFT,NEEDS_YOUR_ATTENTION,CANCELED,APPROVED
     * 
     * @param rpaStatus RPA任务状态
     * @return BD记录状态
     */
    private String mapRpaStatusToBdStatus(Integer rpaStatus) {
        if (rpaStatus == null) {
            return null;
        }
        
        switch (rpaStatus) {
            case 1: // 待处理
                return "DRAFT";
            case 2: // 处理中
                return "NEEDS_YOUR_ATTENTION";
            case 3: // 处理成功
                return "APPROVED";
            case 4: // 处理失败
                return "CANCELED";
            default:
                return null;
        }
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.suncent.smc.persistence.promotion.mapper.AmBestDealRecordMapper">
    
    <resultMap type="AmBestDealRecord" id="AmBestDealRecordResult">
        <result property="id"                    column="id"                    />
        <result property="publishType"           column="publish_type"          />
        <result property="site"                  column="site"                  />
        <result property="promotionId"           column="promotion_id"          />
        <result property="promotionName"         column="promotion_name"        />
        <result property="fundingAgreementId"    column="funding_agreement_id"  />
        <result property="status"                column="status"                />
        <result property="dealType"              column="deal_type"             />
        <result property="eventType"             column="event_type"            />
        <result property="startDateUtc"          column="start_date_utc"        />
        <result property="endDateUtc"            column="end_date_utc"          />
        <result property="createdDateTime"       column="created_date_time"     />
        <result property="lastUpdateDateTime"    column="last_update_date_time" />
        <result property="createBy"              column="create_by"             />
        <result property="createTime"            column="create_time"           />
        <result property="updateBy"              column="update_by"             />
        <result property="updateTime"            column="update_time"           />
        <result property="remark"                column="remark"                />
        <result property="delFlag"               column="del_flag"              />
        <result property="asinCount"             column="asin_count"            />
    </resultMap>

    <sql id="selectAmBestDealRecordVo">
        select id, publish_type, site, promotion_id, promotion_name, funding_agreement_id, status, deal_type, event_type, start_date_utc, end_date_utc, created_date_time, last_update_date_time, create_by, create_time, update_by, update_time, remark, del_flag from am_best_deal_record
    </sql>

    <select id="selectAmBestDealRecordList" parameterType="AmBestDealRecord" resultMap="AmBestDealRecordResult">
        <include refid="selectAmBestDealRecordVo"/>
        <where>
            <if test="publishType != null "> and publish_type = #{publishType}</if>
            <if test="site != null  and site != ''"> and site = #{site}</if>
            <if test="promotionId != null  and promotionId != ''"> and promotion_id = #{promotionId}</if>
            <if test="promotionName != null  and promotionName != ''"> and promotion_name like concat('%', #{promotionName}, '%')</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="dealType != null "> and deal_type = #{dealType}</if>
            <if test="eventType != null "> and event_type = #{eventType}</if>
            <if test="startDateUtc != null  and startDateUtc != ''"> and start_date_utc = #{startDateUtc}</if>
            <if test="endDateUtc != null  and endDateUtc != ''"> and end_date_utc = #{endDateUtc}</if>

            <!-- 促销开始时间范围查询 -->
            <if test="params.startDateFrom != null and params.startDateFrom != ''">
                AND start_date_utc &gt;= #{params.startDateFrom}
            </if>
            <if test="params.startDateTo != null and params.startDateTo != ''">
                AND start_date_utc &lt;= #{params.startDateTo}
            </if>

            <!-- 促销结束时间范围查询 -->
            <if test="params.endDateFrom != null and params.endDateFrom != ''">
                AND end_date_utc &gt;= #{params.endDateFrom}
            </if>
            <if test="params.endDateTo != null and params.endDateTo != ''">
                AND end_date_utc &lt;= #{params.endDateTo}
            </if>

            <!-- 关键字搜索（促销名称、促销ID） -->
            <if test="params.keyword != null and params.keyword != ''">
                AND (promotion_name like concat('%', #{params.keyword}, '%')
                     OR promotion_id like concat('%', #{params.keyword}, '%'))
            </if>

            <!-- 创建时间范围查询 -->
            <if test="params.beginTime != null and params.beginTime != ''">
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>

            and del_flag = 0
        </where>
        order by create_time desc
    </select>

    <select id="selectAmBestDealRecordListWithAsinCount" parameterType="AmBestDealRecord" resultMap="AmBestDealRecordResult">
        select
            r.id, r.publish_type, r.site, r.promotion_id, r.promotion_name,
            r.funding_agreement_id, r.status, r.deal_type, r.event_type,
            r.start_date_utc, r.end_date_utc, r.created_date_time,
            r.last_update_date_time, r.create_by, r.create_time,
            r.update_by, r.update_time, r.remark, r.del_flag,
            (SELECT COUNT(1) FROM am_best_deal_asin a WHERE a.ref_best_deal_id = r.id AND a.del_flag = 0) as asin_count
        from am_best_deal_record r
        <where>
            <if test="publishType != null "> and r.publish_type = #{publishType}</if>
            <if test="site != null  and site != ''"> and r.site = #{site}</if>
            <if test="promotionId != null  and promotionId != ''"> and r.promotion_id = #{promotionId}</if>
            <if test="promotionName != null  and promotionName != ''"> and r.promotion_name like concat('%', #{promotionName}, '%')</if>
            <if test="status != null  and status != ''"> and r.status = #{status}</if>
            <if test="dealType != null "> and r.deal_type = #{dealType}</if>
            <if test="eventType != null "> and r.event_type = #{eventType}</if>
            <if test="startDateUtc != null  and startDateUtc != ''"> and r.start_date_utc = #{startDateUtc}</if>
            <if test="endDateUtc != null  and endDateUtc != ''"> and r.end_date_utc = #{endDateUtc}</if>

            <!-- 促销开始时间范围查询 -->
            <if test="params.startDateFrom != null and params.startDateFrom != ''">
                AND r.start_date_utc &gt;= #{params.startDateFrom}
            </if>
            <if test="params.startDateTo != null and params.startDateTo != ''">
                AND r.start_date_utc &lt;= #{params.startDateTo}
            </if>

            <!-- 促销结束时间范围查询 -->
            <if test="params.endDateFrom != null and params.endDateFrom != ''">
                AND r.end_date_utc &gt;= #{params.endDateFrom}
            </if>
            <if test="params.endDateTo != null and params.endDateTo != ''">
                AND r.end_date_utc &lt;= #{params.endDateTo}
            </if>

            <!-- 关键字搜索（促销名称、促销ID） -->
            <if test="params.keyword != null and params.keyword != ''">
                AND (r.promotion_name like concat('%', #{params.keyword}, '%')
                     OR r.promotion_id like concat('%', #{params.keyword}, '%'))
            </if>

            <!-- 创建时间范围查询 -->
            <if test="params.beginTime != null and params.beginTime != ''">
                AND date_format(r.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''">
                AND date_format(r.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>

            and r.del_flag = 0
        </where>
        order by r.create_time desc
    </select>

    <select id="selectAmBestDealRecordById" parameterType="Long" resultMap="AmBestDealRecordResult">
        <include refid="selectAmBestDealRecordVo"/>
        where id = #{id} and del_flag = 0
    </select>

    <select id="selectAmBestDealRecordByPromotionId" parameterType="String" resultMap="AmBestDealRecordResult">
        <include refid="selectAmBestDealRecordVo"/>
        where promotion_id = #{promotionId} and del_flag = 0
    </select>


    <select id="selectAmBestDealRecordByPromotionIds" resultMap="AmBestDealRecordResult">
        <include refid="selectAmBestDealRecordVo"/>
        where promotion_id in
        <foreach collection="list" item="promotionId" open="(" separator="," close=")">
            #{promotionId}
        </foreach>
        and del_flag = 0
    </select>

    <select id="selectAmBestDealRecordByIds" resultMap="AmBestDealRecordResult">
        <include refid="selectAmBestDealRecordVo"/>
        where id in
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        and del_flag = 0
    </select>
    <insert id="insertAmBestDealRecord" parameterType="AmBestDealRecord" useGeneratedKeys="true" keyProperty="id">
        insert into am_best_deal_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="publishType != null">publish_type,</if>
            <if test="site != null and site != ''">site,</if>
            <if test="promotionId != null and promotionId != ''">promotion_id,</if>
            <if test="promotionName != null and promotionName != ''">promotion_name,</if>
            <if test="fundingAgreementId != null">funding_agreement_id,</if>
            <if test="status != null and status != ''">status,</if>
            <if test="dealType != null">deal_type,</if>
            <if test="eventType != null">event_type,</if>
            <if test="startDateUtc != null and startDateUtc != ''">start_date_utc,</if>
            <if test="endDateUtc != null and endDateUtc != ''">end_date_utc,</if>
            <if test="createdDateTime != null and createdDateTime != ''">created_date_time,</if>
            <if test="lastUpdateDateTime != null and lastUpdateDateTime != ''">last_update_date_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null and remark != ''">remark,</if>
            <if test="smcFlag !=null">smc_flag,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="publishType != null">#{publishType},</if>
            <if test="site != null and site != ''">#{site},</if>
            <if test="promotionId != null and promotionId != ''">#{promotionId},</if>
            <if test="promotionName != null and promotionName != ''">#{promotionName},</if>
            <if test="fundingAgreementId != null">#{fundingAgreementId},</if>
            <if test="status != null and status != ''">#{status},</if>
            <if test="dealType != null">#{dealType},</if>
            <if test="eventType != null">#{eventType},</if>
            <if test="startDateUtc != null and startDateUtc != ''">#{startDateUtc},</if>
            <if test="endDateUtc != null and endDateUtc != ''">#{endDateUtc},</if>
            <if test="createdDateTime != null and createdDateTime != ''">#{createdDateTime},</if>
            <if test="lastUpdateDateTime != null and lastUpdateDateTime != ''">#{lastUpdateDateTime},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null and remark != ''">#{remark},</if>
            <if test="smcFlag !=null">#{smcFlag},</if>
         </trim>
    </insert>

    <update id="updateAmBestDealRecord" parameterType="AmBestDealRecord">
        update am_best_deal_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="publishType != null">publish_type = #{publishType},</if>
            <if test="site != null">site = #{site},</if>
            <if test="promotionId != null">promotion_id = #{promotionId},</if>
            <if test="promotionName != null">promotion_name = #{promotionName},</if>
            <if test="fundingAgreementId != null">funding_agreement_id = #{fundingAgreementId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="dealType != null">deal_type = #{dealType},</if>
            <if test="eventType != null">event_type = #{eventType},</if>
            <if test="startDateUtc != null">start_date_utc = #{startDateUtc},</if>
            <if test="endDateUtc != null">end_date_utc = #{endDateUtc},</if>
            <if test="createdDateTime != null">created_date_time = #{createdDateTime},</if>
            <if test="lastUpdateDateTime != null">last_update_date_time = #{lastUpdateDateTime},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAmBestDealRecordById" parameterType="Long">
        update am_best_deal_record set del_flag = 1 where id = #{id}
    </delete>

    <delete id="deleteAmBestDealRecordByIds" parameterType="String">
        update am_best_deal_record set del_flag = 1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectAmBestDealRecordBySiteAndStatus" resultMap="AmBestDealRecordResult">
        <include refid="selectAmBestDealRecordVo"/>
        where site = #{site} and status = #{status} and del_flag = 0
        order by create_time desc
    </select>

    <!-- 统计查询 -->
    <select id="getStatsBySite" resultType="java.util.Map">
        select site, count(*) as count
        from am_best_deal_record
        where del_flag = 0
        group by site
        order by count desc
    </select>

    <select id="getStatsByStatus" resultType="java.util.Map">
        select status, count(*) as count
        from am_best_deal_record
        where del_flag = 0
        group by status
        order by count desc
    </select>

    <select id="getStatsByPublishType" resultType="java.util.Map">
        select
            publish_type,
            case
                when publish_type = 5 then 'VCDF'
                when publish_type = 6 then 'VCPO'
                else 'Unknown'
            end as publish_type_name,
            count(*) as count
        from am_best_deal_record
        where del_flag = 0
        group by publish_type
        order by count desc
    </select>

    <select id="getTotalCount" resultType="int">
        select count(*) from am_best_deal_record where del_flag = 0
    </select>

    <select id="getRecentCreatedCount" parameterType="int" resultType="int">
        select count(*)
        from am_best_deal_record
        where del_flag = 0
        and create_time >= date_sub(now(), interval #{days} day)
    </select>

    <!-- 获取总ASIN数量 -->
    <select id="getTotalAsinCount" resultType="int">
        select count(1) from am_best_deal_asin
        where del_flag = 0
    </select>

    <!-- 根据状态获取记录数量 -->
    <select id="getCountByStatus" parameterType="String" resultType="int">
        select count(1) from am_best_deal_record
        where status = #{status}
        and del_flag = 0
    </select>

    <!-- 获取创建趋势（最近N天） -->
    <select id="getCreateTrend" parameterType="int" resultType="java.util.Map">
        select
            DATE_FORMAT(create_time, '%Y-%m-%d') as date,
            count(1) as count
        from am_best_deal_record
        where create_time >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
        and del_flag = 0
        group by DATE_FORMAT(create_time, '%Y-%m-%d')
        order by date
    </select>

    <!-- 根据促销ID更新状态 -->
    <update id="updateStatusByPromotionId">
        update am_best_deal_record
        set status = #{status},
            update_by = #{updateBy},
            update_time = now()
        where promotion_id = #{promotionId}
        and del_flag = 0
    </update>

</mapper>

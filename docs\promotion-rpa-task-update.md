# 促销RPA任务表结构更新说明

## 更新概述

`am_promotion_rpa_task` 表新增了 `promotion_status` 字段，用于直接存储促销状态，避免通过数字状态码映射的复杂性。

## 表结构变更

### 新增字段
```sql
ALTER TABLE `am_promotion_rpa_task` 
ADD COLUMN `promotion_status` varchar(64) DEFAULT NULL COMMENT '活动状态' 
AFTER `promotion_id`;
```

### 完整表结构
```sql
CREATE TABLE `am_promotion_rpa_task` (
  `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `ref_best_deal_id` bigint(20) NOT NULL,
  `operation_type` varchar(32) DEFAULT NULL COMMENT 'ADD 创建,UPDATE 更新,DELETE取消',
  `promotion_id` varchar(64) DEFAULT NULL COMMENT '促销ID，亚马逊后台的ID',
  `promotion_status` varchar(64) DEFAULT NULL COMMENT '活动状态',  -- 新增字段
  `publish_type` tinyint(4) NOT NULL COMMENT '刊登类型，5：VCDF，6：VCPO',
  `site` varchar(10) NOT NULL DEFAULT 'US' COMMENT '站点',
  `activity_type` tinyint(4) NOT NULL COMMENT '活动类型,1:BD,2:Coupon,3:PD',
  `execute_json` text COMMENT '执行任务JSON',
  `status` tinyint(4) NOT NULL COMMENT '状态：1：待处理，2：处理中，3：处理成功，4：处理失败',
  `create_by` varchar(20) NOT NULL COMMENT '创建人',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_by` varchar(20) DEFAULT NULL COMMENT '修改人',
  `update_time` datetime DEFAULT NULL COMMENT '修改时间',
  `handle_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT 'smc处理标识',
  `error_msg` varchar(255) DEFAULT NULL COMMENT '错误消息',
  `remark` varchar(200) DEFAULT NULL COMMENT '备注',
  `del_flag` tinyint(1) NOT NULL DEFAULT '0' COMMENT '删除标记,1是，0否',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4;
```

## 代码更新

### 1. 实体类更新
在 `AmPromotionRpaTask.java` 中新增字段：
```java
/**
 * 活动状态
 */
@Excel(name = "活动状态")
private String promotionStatus;
```

### 2. Mapper XML 更新
在 `AmPromotionRpaTaskMapper.xml` 中：
- 添加 `promotion_status` 字段映射
- 更新查询、插入、更新语句

### 3. 定时任务逻辑更新
`PromotionRpaStatusSyncTask` 的处理逻辑更新为：

```java
// 优先使用 promotion_status 字段，如果为空则使用 status 字段映射
String bdStatus = null;

if (task.getPromotionStatus() != null && !task.getPromotionStatus().trim().isEmpty()) {
    // 直接使用 promotion_status 作为 BD 记录状态
    bdStatus = task.getPromotionStatus().trim();
} else {
    // 根据RPA任务status字段映射到BD记录状态
    bdStatus = mapRpaStatusToBdStatus(task.getStatus());
}
```

### 4. 新增服务方法
添加支持 `promotion_status` 的创建方法：
```java
public int createPromotionRpaTaskWithStatus(Long refBestDealId, String operationType, 
                                           String promotionId, String promotionStatus, 
                                           Integer publishType, String site, 
                                           Integer activityType, String executeJson, 
                                           String createBy);
```

## 业务逻辑说明

### 状态处理优先级
1. **优先使用 promotion_status**：如果该字段有值且不为空，直接使用作为 BD 记录状态
2. **备用 status 映射**：如果 promotion_status 为空，则使用原有的 status 字段映射逻辑

### 状态映射规则（备用）
当 `promotion_status` 为空时，使用以下映射：

| RPA任务status | 状态描述 | BD记录状态 | 状态描述 |
|--------------|----------|-----------|----------|
| 1 | 待处理 | DRAFT | 草稿 |
| 2 | 处理中 | NEEDS_YOUR_ATTENTION | 需要关注 |
| 3 | 处理成功 | APPROVED | 已批准 |
| 4 | 处理失败 | CANCELED | 已取消 |

### promotion_status 可能的值
直接对应 BD 记录的状态值：
- `DRAFT` - 草稿
- `NEEDS_YOUR_ATTENTION` - 需要关注
- `CANCELED` - 已取消
- `APPROVED` - 已批准

## 使用示例

### 创建带 promotion_status 的任务
```java
// 使用新方法创建任务
amPromotionRpaTaskService.createPromotionRpaTaskWithStatus(
    1L,                    // refBestDealId
    "ADD",                 // operationType
    "PROMO001",           // promotionId
    "APPROVED",           // promotionStatus - 直接指定状态
    5,                    // publishType
    "US",                 // site
    1,                    // activityType
    "{}",                 // executeJson
    "system"              // createBy
);
```

### 创建使用映射的任务
```java
// 使用原有方法，通过 status 映射
amPromotionRpaTaskService.createPromotionRpaTask(
    1L,                    // refBestDealId
    "ADD",                 // operationType
    "PROMO002",           // promotionId
    5,                    // publishType
    "US",                 // site
    1,                    // activityType
    "{}",                 // executeJson
    "system"              // createBy
);
// 此时 promotion_status 为 null，定时任务会使用 status=1 映射为 "DRAFT"
```

## 兼容性说明

### 向后兼容
- 原有的 `status` 字段和映射逻辑保持不变
- 现有数据不受影响，定时任务会自动使用 status 映射
- 原有的创建方法 `createPromotionRpaTask` 继续可用

### 迁移建议
1. **新业务**：建议使用 `createPromotionRpaTaskWithStatus` 方法直接指定状态
2. **现有业务**：可以逐步迁移，或保持现状使用映射逻辑
3. **数据清理**：可以考虑将现有数据的 status 转换为 promotion_status

## 监控和日志

定时任务会记录使用哪种方式确定状态：
```
INFO - 使用promotion_status字段更新BD状态，任务ID：1，promotion_status：APPROVED
INFO - 使用status字段映射BD状态，任务ID：2，status：1，映射后：DRAFT
```

## 注意事项

1. **数据一致性**：确保 promotion_status 的值与 BD 记录表的状态值一致
2. **空值处理**：promotion_status 为 null 或空字符串时会使用 status 映射
3. **状态验证**：建议在业务层添加状态值的有效性验证
4. **索引优化**：如果查询频繁，可考虑为 promotion_status 添加索引
